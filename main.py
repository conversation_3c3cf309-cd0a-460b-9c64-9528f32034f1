import os
import sys
from pathlib import Path
from marker.converters.pdf import PdfConverter
from marker.models import create_model_dict
from marker.output import text_from_rendered


def load_env_file():
    """Load environment variables from .env file"""
    env_path = Path(".env")
    if env_path.exists():
        print("Loading environment variables from .env file...")
        with open(env_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    # Only set if not already in environment
                    if key not in os.environ:
                        os.environ[key] = value
                        print(f"Set {key} from .env file")
    else:
        print("No .env file found")


def test_gemini_connectivity():
    """Test Gemini 2.5 Flash API connectivity and error handling"""
    print("\n=== Testing Gemini 2.5 Flash Connectivity ===")
    
    # Check API key
    gemini_key = os.getenv("GEMINI_API_KEY")
    if not gemini_key or gemini_key == "your_gemini_api_key_here":
        print("❌ GEMINI_API_KEY not configured")
        return False
    
    print(f"✓ API Key found (length: {len(gemini_key)})")
    
    try:
        # Test by creating a simple converter with LLM enabled
        from marker.models import create_model_dict
        from marker.config.parser import ConfigParser
        
        # Create configuration for Gemini 2.5 Flash
        config = {
            "use_llm": True,
            "gemini_api_key": gemini_key,
            "gemini_model": "gemini-2.5-flash",
            "max_concurrency": 1  # Limit concurrent requests for testing
        }
        
        print("✓ Creating LLM configuration...")
        config_parser = ConfigParser(config)
        llm_service = config_parser.get_llm_service()
        
        if llm_service:
            print("✓ LLM service initialized successfully")
            print(f"✓ Using model: gemini-2.5-flash")
            return True
        else:
            print("❌ Failed to initialize LLM service")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        error_msg = str(e).lower()
        
        # Check for specific API errors
        if "401" in error_msg or "unauthorized" in error_msg:
            print(f"❌ Authentication error: Invalid API key")
        elif "403" in error_msg or "forbidden" in error_msg:
            print(f"❌ Permission error: API key lacks required permissions")
        elif "429" in error_msg or "rate limit" in error_msg:
            print(f"❌ Rate limit error: Too many requests")
        elif "500" in error_msg or "internal server error" in error_msg:
            print(f"❌ Server error: Gemini API internal error")
        elif "502" in error_msg or "bad gateway" in error_msg:
            print(f"❌ Network error: Bad gateway")
        elif "503" in error_msg or "service unavailable" in error_msg:
            print(f"❌ Service error: Gemini API temporarily unavailable")
        elif "timeout" in error_msg:
            print(f"❌ Timeout error: Request timed out")
        elif "network" in error_msg or "connection" in error_msg:
            print(f"❌ Network error: Check internet connection")
        else:
            print(f"❌ Unknown error: {e}")
        
        return False


def simple_conversion(pdf_path: str, use_llm: bool = True):
    """簡單的 PDF 轉換功能"""
    try:
        # Initialize converter with proper LLM configuration
        if use_llm:
            print("Initializing with LLM support (Gemini)...")
            from marker.config.parser import ConfigParser
            
            # 創建 LLM 配置
            config_dict = {
                "use_llm": True,
                "gemini_api_key": os.environ.get('GEMINI_API_KEY'),
                "gemini_model": "gemini-2.5-flash",
                "format_lines": True,
                "output_format": "markdown"
            }
            config_parser = ConfigParser(config_dict)
            
            # 創建轉換器（帶 LLM）
            converter = PdfConverter(
                artifact_dict=create_model_dict(),
                config=config_parser.generate_config_dict(),
                processor_list=config_parser.get_processors(),
                renderer=config_parser.get_renderer()
            )
        else:
            print("Initializing without LLM...")
            # 創建轉換器（不帶 LLM）
            converter = PdfConverter(
                artifact_dict=create_model_dict()
            )
        
        # Convert PDF
        print("Converting PDF to markdown...")
        rendered = converter(pdf_path)
        
        # Extract text from rendered result with improved error handling
        try:
            result = text_from_rendered(rendered)
            
            # Handle different return types
            if isinstance(result, tuple) and len(result) == 3:
                # Standard tuple format: (text, metadata, images)
                text, metadata, images = result
            elif hasattr(result, 'markdown'):
                # MarkdownOutput object
                text = result.markdown
                metadata = getattr(result, 'metadata', None)
                images = getattr(result, 'images', [])
            elif isinstance(result, str):
                # Direct string return
                text = result
                metadata = None
                images = []
            else:
                # Unknown format, try to convert to string
                text = str(result)
                metadata = None
                images = []
                print(f"⚠️  Unexpected return type from text_from_rendered: {type(result)}")
            
            # Ensure text is a string
            if not isinstance(text, str):
                text = str(text)
            
            # Ensure images is a list
            if images is None:
                images = []
            elif not isinstance(images, list):
                images = []
                
        except Exception as extract_error:
            print(f"Error extracting text: {str(extract_error)}")
            print(f"Rendered type: {type(rendered)}")
            # Fallback: try to get text directly from rendered
            if hasattr(rendered, 'markdown'):
                text = rendered.markdown
            else:
                text = str(rendered)
            metadata = None
            images = []
        
        # Save output
        output_path = Path(pdf_path).stem + "_output.md"
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(text)
        
        print(f"Conversion completed! Output saved to: {output_path}")
        print(f"Pages processed: {len(rendered) if hasattr(rendered, '__len__') else 'N/A'}")
        print(f"Images extracted: {len(images) if isinstance(images, list) else 'N/A'}")
        print(f"Output file size: {len(text)} characters")
        
        # Show preview of converted text
        print("\n--- Preview of converted text (first 500 chars) ---")
        preview_text = text[:500] + "..." if len(text) > 500 else text
        print(preview_text)
        
    except Exception as e:
        error_msg = str(e)
        print(f"Error during conversion: {error_msg}")
        
        # Detailed error analysis for LLM/Gemini issues
        error_lower = error_msg.lower()
        if "gemini_api_key" in error_lower or "api key" in error_lower:
            print("💡 Solution: Make sure your GEMINI_API_KEY is correctly set in .env file")
        elif "401" in error_msg or "unauthorized" in error_lower:
            print("💡 Solution: Your Gemini API key is invalid or expired")
        elif "403" in error_msg or "forbidden" in error_lower:
            print("💡 Solution: Your Gemini API key lacks required permissions")
        elif "429" in error_msg or "rate limit" in error_lower:
            print("💡 Solution: You've hit Gemini API rate limits. Wait a moment and try again")
        elif "500" in error_msg or "internal server error" in error_lower:
            print("💡 Solution: Gemini API is experiencing issues. Try again later")
        elif "502" in error_msg or "bad gateway" in error_lower:
            print("💡 Solution: Network connectivity issue. Check your internet connection")
        elif "503" in error_msg or "service unavailable" in error_lower:
            print("💡 Solution: Gemini API service is temporarily unavailable")
        elif "timeout" in error_lower:
            print("💡 Solution: Request timed out. Try with a smaller PDF or check connection")
        elif "markdownoutput" in error_lower:
            print("💡 Solution: Fixed in this version - MarkdownOutput object handling improved")
        elif "quota" in error_lower or "billing" in error_lower:
            print("💡 Solution: Check your Google Cloud billing and API quotas")
        elif use_llm:
            print("💡 Try running without LLM (set use_llm=False) to isolate the issue")
        
        return


def main():
    print("Hello from OCR with marker-pdf!")
    
    # Load environment variables from .env file
    load_env_file()
    
    # Check GEMINI_API_KEY
    gemini_key = os.getenv("GEMINI_API_KEY")
    if not gemini_key or gemini_key == "your_gemini_api_key_here":
        print("⚠️  GEMINI_API_KEY not properly configured!")
        print("Please edit .env file and set your actual Gemini API key")
        print("Current value:", gemini_key)
        
        # Prompt for key as fallback
        new_key = input("Enter your GEMINI API key (or press Enter to skip LLM): ").strip()
        if new_key:
            os.environ["GEMINI_API_KEY"] = new_key
            print("✅ GEMINI_API_KEY set for this session")
            use_llm = True
        else:
            use_llm = False
            print("Proceeding without LLM services")
    else:
        use_llm = True
    
    # Check if we have command line arguments
    if len(sys.argv) > 1:
        # Direct conversion mode
        pdf_path = sys.argv[1]
        if not os.path.exists(pdf_path):
            print(f"Error: File '{pdf_path}' not found")
            return
        
        print(f"Processing PDF: {pdf_path}")
        print(f"LLM services enabled: {use_llm}")
        simple_conversion(pdf_path, use_llm)
    else:
        # Interactive mode
        print("\n選擇操作模式:")
        print("1. 簡單 PDF 轉換 (使用 PDFConverter)")
        print("2. 完整測試比較 (測試所有 Converter)")
        print("3. 測試 Gemini 2.5 Flash 連接")
        print("4. 退出")
        
        choice = input("\n請選擇 (1-4): ").strip()
        
        if choice == "1":
            # Simple conversion
            pdf_path = input("請輸入 PDF 文件路徑 (或按 Enter 使用 sample.pdf): ").strip()
            if not pdf_path:
                pdf_path = "sample.pdf"
            
            if not os.path.exists(pdf_path):
                print(f"Error: File '{pdf_path}' not found")
                return
            
            print(f"Processing PDF: {pdf_path}")
            print(f"LLM services enabled: {use_llm}")
            simple_conversion(pdf_path, use_llm)
            
        elif choice == "2":
            # Comprehensive test
            from ocrtest import test_single_file
            
            pdf_path = input("請輸入要測試的 PDF 文件路徑 (或按 Enter 使用 sample.pdf): ").strip()
            if not pdf_path:
                pdf_path = "sample.pdf"
            
            if not os.path.exists(pdf_path):
                print(f"Error: File '{pdf_path}' not found")
                return
            
            output_dir = input("請輸入輸出目錄 (或按 Enter 使用默認): ").strip()
            if not output_dir:
                output_dir = "./converter_comparison"
            
            print(f"\n開始測試 '{pdf_path}'...")
            results = test_single_file(pdf_path, use_gemini=use_llm, output_dir=output_dir)
            
            if results:
                print(f"\n測試完成！詳細結果保存在: {output_dir}")
            
        elif choice == "3":
            # Test Gemini connectivity
            gemini_ok = test_gemini_connectivity()
            if gemini_ok:
                print("\n✅ Gemini 2.5 Flash connectivity test passed!")
            else:
                print("\n❌ Gemini 2.5 Flash connectivity test failed!")
            
        elif choice == "4":
            print("退出程序")
            return
        else:
            print("無效的選擇")


if __name__ == "__main__":
    main()
