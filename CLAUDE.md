# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Python OCR (Optical Character Recognition) project using uv for dependency management. The project provides comprehensive testing and comparison of different marker-pdf converters with robust LLM support via Gemini 2.5 Flash.

## Development Setup

- Python version: 3.12 (specified in `.python-version`)
- Package manager: uv (uses `uv.lock` for dependency locking)
- Primary dependency: marker-pdf>=1.7.5
- Additional dependencies: torchvision (required for comprehensive testing)

## Common Commands

### Environment and Dependencies
```bash
# Install dependencies
uv sync

# Run the main application
uv run python main.py

# Run comprehensive converter testing
uv run python ocrtest.py sample.pdf

# Add new dependencies
uv add <package-name>

# Update dependencies
uv sync --upgrade
```

### Development
```bash
# Run the main application with different modes
python main.py                    # Interactive mode
python main.py sample.pdf         # Direct conversion
```

## Architecture

- **main.py**: Enhanced OCR application with MarkdownOutput error fixes and Gemini connectivity testing
- **ocrtest.py**: Comprehensive converter testing framework comparing all marker-pdf converter types
- **pyproject.toml**: Project configuration and dependencies
- Uses marker-pdf library with multiple converter types: PDFConverter, TableConverter, OCRConverter

## Environment Configuration

The project uses a `.env` file for environment variable management:

- **`.env`**: Contains GEMINI_API_KEY and other configuration
- **`main.py`**: Automatically loads variables from .env file
- No external dependencies needed (custom .env loader)

### Environment Variables

```bash
# .env file structure
GEMINI_API_KEY=your_actual_api_key_here
USE_LLM=true
```

## Converter Types Tested

### Working Converters ✅
1. **PDFConverter (without LLM)**: Fast, reliable (~5.2s, 9,932 chars)
2. **PDFConverter (with Gemini 2.5 Flash)**: High quality but slower (~52s, 9,991 chars) 
3. **TableConverter**: Excellent for table extraction (~3.7s, 1,071 chars)
4. **CLI TableConverter**: Command-line alternative (~8s)

### Known Issues ❌
1. **OCRConverter**: Has NoneType bug in marker-pdf library (`'NoneType' object is not iterable`)
2. **CLI OCRConverter**: Same upstream bug affects command-line usage

## LLM Testing & Error Handling

### Gemini 2.5 Flash Integration
- **Enhanced error handling** for API issues (401, 403, 429, 500, 502, 503)
- **Connectivity testing** via menu option 3
- **MarkdownOutput error fixed** - robust type handling for different return formats
- **Detailed diagnostics** with solution suggestions

### Interactive Testing
```bash
uv run python main.py
# Select option 3: "測試 Gemini 2.5 Flash 連接" 
```

### Performance Comparison
```bash
# Run comprehensive converter comparison
uv run python ocrtest.py sample.pdf

# Quick test without LLM (faster)
uv run python -c "
from ocrtest import test_single_file
test_single_file('sample.pdf', use_gemini=False)
"
```

## Error Resolution

### Fixed Issues
- **MarkdownOutput Error**: `object of type 'MarkdownOutput' has no len()` - RESOLVED
- **CLI Argument Parsing**: Fixed marker_single command syntax
- **Type Handling**: Added comprehensive type checking for different output formats

### API Error Codes
- `401`: Invalid API key → Check GEMINI_API_KEY in .env
- `403`: Insufficient permissions → Verify API key permissions
- `429`: Rate limit → Wait and retry
- `502/503`: Service issues → Retry later

## Testing Strategy

### Recommended Usage
1. **Fast conversion**: Use PDFConverter without LLM
2. **Table extraction**: Use TableConverter
3. **Highest quality**: Use PDFConverter with Gemini LLM (slower)
4. **Avoid**: OCRConverter (has upstream bug)

### Test Reports
Generated comparison reports in `converter_comparison/comparison_report.md` with:
- Performance metrics (processing time, output size)
- Success/failure status
- Feature comparisons

## Dependencies & Setup

### Required Files
- `sample.pdf`: Test document (place in project root)
- `.env`: Environment configuration with GEMINI_API_KEY
- `torchvision`: Required for comprehensive testing

### Installation
```bash
uv sync
uv add torchvision  # If not already installed
```

## Updates

- Fixed MarkdownOutput error in main.py with comprehensive type handling
- Added Gemini 2.5 Flash connectivity testing and detailed error diagnostics
- Enhanced ocrtest.py with CLI fixes and better error reporting
- Documented working vs. problematic converter types