# Marker PDF 转换器使用指南

## 快速开始

### 1. 基础转换（推荐）
```bash
uv run python marker_with_proxy.py
```
这将使用 SOCKS5 代理转换 `sample.pdf` 文件。

### 2. 高级功能测试
```bash
# 测试代理连接
uv run python marker_proxy_advanced.py --test-proxy

# 使用高级版本转换
uv run python marker_proxy_advanced.py
```

## 命令选项

### marker_with_proxy.py
```bash
uv run python marker_with_proxy.py                    # 转换 sample.pdf
uv run python marker_with_proxy.py your_file.pdf      # 转换指定文件
uv run python marker_with_proxy.py --no-proxy         # 不使用代理
```

### marker_proxy_advanced.py
```bash
uv run python marker_proxy_advanced.py                # 转换 sample.pdf
uv run python marker_proxy_advanced.py your_file.pdf  # 转换指定文件
uv run python marker_proxy_advanced.py --no-proxy     # 不使用代理
uv run python marker_proxy_advanced.py --test-proxy   # 仅测试代理
```

## 配置要求

### 1. 必需文件
- `sample.pdf` - 要转换的 PDF 文件
- `config.json` - 配置文件

### 2. config.json 示例
```json
{
    "use_llm": true,
    "gemini_api_key": "your_gemini_api_key_here",
    "gemini_model_name": "gemini-2.5-flash",
    "force_ocr": true,
    "format_lines": true,
    "redo_inline_math": true,
    "output_format": "markdown",
    "disable_image_extraction": false,
    "paginate_output": true,
    "debug": true
}
```

## 代理配置

### NordVPN SOCKS5 代理
脚本中已预配置 NordVPN 的 SOCKS5 代理：
```python
proxy_url = "socks5://username:password@server:port"
```

### 自定义代理
如需使用其他代理，请修改脚本中的 `proxy_url` 变量。

## 输出结果

### 文件结构
```
textbook_output/
└── sample/
    ├── sample.md           # 转换后的 Markdown 文件
    ├── metadata.json       # 转换元数据
    ├── sample_meta.json    # 额外元数据
    ├── images/             # 提取的图片目录
    └── _page_X_Picture_Y.jpeg  # 页面图片
```

### 元数据示例
```json
{
  "input_file": "sample.pdf",
  "output_dir": "textbook_output/sample",
  "processing_time": 73.62,
  "text_length": 10201,
  "num_images": 1,
  "success": true,
  "proxy_used": true
}
```

## 功能特性

### ✅ 已验证功能
- PDF 到 Markdown 转换
- 图片提取和保存
- SOCKS5 代理支持
- Gemini 2.5 Flash LLM 增强
- 自动目录创建
- 详细的处理日志

### ⚠️ 已知限制
- Google AI SDK 对 SOCKS5 代理支持有限
- 某些复杂 PDF 可能需要更长处理时间
- 需要有效的 Gemini API 密钥

## 故障排除

### 1. 模块未找到错误
```
ModuleNotFoundError: No module named 'torch'
```
**解决方案**: 确保使用 `uv run python` 而不是直接 `python`

### 2. 代理连接问题
```
User location is not supported for the API use
```
**解决方案**: 
- 检查 VPN/代理是否正常工作
- 验证代理服务器地址和认证信息
- 尝试使用 `--test-proxy` 选项测试连接

### 3. API 密钥问题
```
✗ Gemini API 连接失败
```
**解决方案**: 
- 检查 `config.json` 中的 `gemini_api_key`
- 确保 API 密钥有效且有足够配额
- 验证网络连接

### 4. 文件不存在
```
错误：PDF 文件 'sample.pdf' 不存在
```
**解决方案**: 
- 确保 PDF 文件存在于当前目录
- 使用完整路径指定文件
- 检查文件权限

## 性能优化

### 1. 处理大文件
- 对于大型 PDF，考虑增加超时时间
- 监控内存使用情况
- 考虑分批处理

### 2. 网络优化
- 使用稳定的代理服务器
- 考虑本地缓存机制
- 监控网络延迟

## 高级用法

### 1. 批量处理
```python
# 自定义脚本示例
import os
from pathlib import Path

pdf_files = Path(".").glob("*.pdf")
for pdf_file in pdf_files:
    os.system(f"uv run python marker_with_proxy.py {pdf_file}")
```

### 2. 自定义配置
```python
# 修改脚本中的配置
config = {
    "use_llm": True,
    "gemini_model_name": "gemini-2.5-flash",
    "output_format": "markdown",
    # 添加其他配置...
}
```

## 支持和反馈

如果遇到问题：
1. 检查本指南的故障排除部分
2. 查看 `README_PROXY.md` 了解技术细节
3. 使用 `--test-proxy` 选项诊断代理问题
4. 检查日志输出中的错误信息

## 更新日志

- **v1.0**: 基础 PDF 转换功能
- **v1.1**: 添加 SOCKS5 代理支持
- **v1.2**: 修复 httpx 代理参数问题
- **v1.3**: 添加高级代理管理和测试功能
