#!/usr/bin/env python3
"""
Marker PDF 转换器 - 带 SOCKS5 代理支持

使用方法:
    uv run python marker_with_proxy.py

功能:
- 使用 marker-pdf 转换 PDF 到 Markdown
- 支持 SOCKS5 代理（NordVPN）
- 支持 Gemini 2.5 Flash LLM 增强
- 模拟 CLI 命令: uv run marker_single sample.pdf --config_json config.json --output_dir ./textbook_output
"""

import os
import sys
import json
import time
import socks
import socket
from pathlib import Path
from typing import Dict, Any
import traceback


def setup_socks5_proxy(proxy_url: str):
    """配置 SOCKS5 代理"""
    from urllib.parse import urlparse
    
    parsed = urlparse(proxy_url)
    username = parsed.username
    password = parsed.password
    host = parsed.hostname
    port = parsed.port
    
    print(f"配置 SOCKS5 代理: {host}:{port}")
    
    # 设置环境变量
    os.environ['https_proxy'] = proxy_url
    os.environ['http_proxy'] = proxy_url
    os.environ['HTTPS_PROXY'] = proxy_url
    os.environ['HTTP_PROXY'] = proxy_url
    
    # 设置 socks 代理
    try:
        socks.set_default_proxy(socks.SOCKS5, host, port, username=username, password=password)
        socket.socket = socks.socksocket
        print("✓ SOCKS5 代理配置成功")
        return True
    except Exception as e:
        print(f"⚠️ SOCKS5 代理配置失败: {e}")
        return False


def convert_pdf_with_marker(
    pdf_path: str,
    config_path: str = "config.json",
    output_dir: str = "./textbook_output",
    proxy_url: str = None
) -> Dict[str, Any]:
    """
    使用 marker-pdf 转换 PDF，支持代理
    """
    print(f"\n=== Marker PDF 转换器 (Python API) ===")
    print(f"PDF 文件: {pdf_path}")
    print(f"配置文件: {config_path}")
    print(f"输出目录: {output_dir}")
    if proxy_url:
        print(f"代理: {proxy_url}")
    
    try:
        # 1. 设置代理
        if proxy_url:
            setup_socks5_proxy(proxy_url)
        
        # 2. 检查文件
        if not os.path.exists(pdf_path):
            return {"success": False, "error": f"PDF 文件不存在: {pdf_path}"}
        
        if not os.path.exists(config_path):
            return {"success": False, "error": f"配置文件不存在: {config_path}"}
        
        # 3. 加载配置
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print(f"✓ 加载配置: {config}")
        
        # 4. 设置 API Key 环境变量
        if config.get("gemini_api_key"):
            os.environ["GEMINI_API_KEY"] = config["gemini_api_key"]
        
        # 5. 创建输出目录
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 6. 导入 marker 模块
        from marker.converters.pdf import PdfConverter
        from marker.models import create_model_dict
        from marker.output import text_from_rendered
        from marker.config.parser import ConfigParser
        
        # 7. 创建配置解析器和转换器
        start_time = time.time()
        
        config_parser = ConfigParser(config)
        converter = PdfConverter(
            artifact_dict=create_model_dict(),
            config=config_parser.generate_config_dict(),
            processor_list=config_parser.get_processors(),
            renderer=config_parser.get_renderer()
        )
        
        print("✓ 转换器创建成功，开始处理 PDF...")
        
        # 8. 执行转换
        rendered = converter(pdf_path)
        text, _, images = text_from_rendered(rendered)
        
        end_time = time.time()
        
        # 9. 保存结果
        pdf_name = Path(pdf_path).stem
        output_subdir = output_path / pdf_name
        output_subdir.mkdir(exist_ok=True)
        
        # 保存 Markdown
        markdown_path = output_subdir / f"{pdf_name}.md"
        with open(markdown_path, "w", encoding="utf-8") as f:
            f.write(text)
        
        # 保存元数据
        metadata = {
            "input_file": pdf_path,
            "output_dir": str(output_subdir),
            "config_used": config,
            "processing_time": end_time - start_time,
            "text_length": len(text),
            "num_images": len(images) if images else 0,
            "success": True,
            "proxy_used": proxy_url is not None
        }
        
        metadata_path = output_subdir / "metadata.json"
        with open(metadata_path, "w", encoding="utf-8") as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        print(f"✓ 转换完成！")
        print(f"✓ 处理时间: {end_time - start_time:.2f}秒")
        print(f"✓ 文本长度: {len(text)} 字符")
        print(f"✓ 提取图片: {len(images) if images else 0} 张")
        print(f"✓ 输出保存到: {output_subdir}")
        print(f"✓ Markdown 文件: {markdown_path}")
        
        return metadata
        
    except Exception as e:
        print(f"✗ 转换失败: {str(e)}")
        traceback.print_exc()
        return {"success": False, "error": str(e)}


def main():
    """主函数"""
    print("=== Marker PDF 转换器 (带 SOCKS5 代理支持) ===\n")
    
    # 配置参数
    pdf_file = "sample.pdf"
    config_file = "config.json"
    output_dir = "./textbook_output"
    
    # NordVPN SOCKS5 代理
    proxy_url = "socks5://2qfCDkqLEw8P3kf4ZAdm5zMu:<EMAIL>:1080"
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == "--no-proxy":
            proxy_url = None
            print("⚠️ 禁用代理模式")
        elif sys.argv[1].endswith('.pdf'):
            pdf_file = sys.argv[1]
    
    # 检查文件
    if not os.path.exists(pdf_file):
        print(f"错误：PDF 文件 '{pdf_file}' 不存在")
        print("用法:")
        print(f"  uv run python {sys.argv[0]}                    # 转换 sample.pdf")
        print(f"  uv run python {sys.argv[0]} your_file.pdf      # 转换指定文件")
        print(f"  uv run python {sys.argv[0]} --no-proxy         # 不使用代理")
        return 1
    
    if not os.path.exists(config_file):
        print(f"错误：配置文件 '{config_file}' 不存在")
        return 1
    
    print(f"PDF 文件: {pdf_file}")
    print(f"配置文件: {config_file}")
    print(f"输出目录: {output_dir}")
    if proxy_url:
        print(f"代理: {proxy_url}")
    else:
        print("代理: 禁用")
    print()
    
    # 执行转换
    result = convert_pdf_with_marker(
        pdf_path=pdf_file,
        config_path=config_file,
        output_dir=output_dir,
        proxy_url=proxy_url
    )
    
    if result.get("success", False):
        print("\n🎉 转换成功完成！")
        print(f"输出目录: {result.get('output_dir', output_dir)}")
        return 0
    else:
        print(f"\n❌ 转换失败: {result.get('error', '未知错误')}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
