#!/usr/bin/env python3
"""
Marker PDF 转换器 - 高级代理支持版本

使用方法:
    uv run python marker_proxy_advanced.py

功能:
- 使用 marker-pdf 转换 PDF 到 Markdown
- 支持 SOCKS5 代理（NordVPN）
- 支持 Gemini 2.5 Flash LLM 增强
- 解决 Google AI SDK 的代理兼容性问题
"""

import os
import sys
import json
import time
import socks
import socket
import subprocess
import threading
from pathlib import Path
from typing import Dict, Any, Optional
import traceback


class ProxyManager:
    """代理管理器"""
    
    def __init__(self, socks5_url: str):
        self.socks5_url = socks5_url
        self.http_proxy_port = 8888  # 本地 HTTP 代理端口
        self.proxy_process = None
        
    def setup_socks5_proxy(self):
        """配置 SOCKS5 代理"""
        from urllib.parse import urlparse
        
        parsed = urlparse(self.socks5_url)
        username = parsed.username
        password = parsed.password
        host = parsed.hostname
        port = parsed.port
        
        print(f"配置 SOCKS5 代理: {host}:{port}")
        
        # 设置环境变量
        os.environ['https_proxy'] = self.socks5_url
        os.environ['http_proxy'] = self.socks5_url
        os.environ['HTTPS_PROXY'] = self.socks5_url
        os.environ['HTTP_PROXY'] = self.socks5_url
        
        # 设置 socks 代理
        try:
            socks.set_default_proxy(socks.SOCKS5, host, port, username=username, password=password)
            socket.socket = socks.socksocket
            print("✓ SOCKS5 代理配置成功")
            return True
        except Exception as e:
            print(f"⚠️ SOCKS5 代理配置失败: {e}")
            return False
    
    def start_http_proxy_bridge(self):
        """启动 HTTP 代理桥接（如果需要）"""
        # 这里可以实现 SOCKS5 到 HTTP 的转换
        # 目前先使用环境变量方式
        pass
    
    def test_connection(self):
        """测试代理连接"""
        try:
            import httpx

            # 修正 httpx 代理参数格式
            proxies = self.socks5_url

            with httpx.Client(proxies=proxies, timeout=30.0) as client:
                response = client.get("https://httpbin.org/ip")
                if response.status_code == 200:
                    ip_info = response.json()
                    print(f"✓ 代理连接成功，当前 IP: {ip_info.get('origin', 'Unknown')}")
                    return True
                else:
                    print(f"✗ 代理连接失败，状态码: {response.status_code}")
                    return False
        except Exception as e:
            print(f"✗ 代理连接测试失败: {e}")
            # 尝试备用方法
            try:
                import requests
                proxies = {
                    'http': self.socks5_url,
                    'https': self.socks5_url
                }
                response = requests.get("https://httpbin.org/ip", proxies=proxies, timeout=30)
                if response.status_code == 200:
                    ip_info = response.json()
                    print(f"✓ 代理连接成功（备用方法），当前 IP: {ip_info.get('origin', 'Unknown')}")
                    return True
            except Exception as e2:
                print(f"✗ 备用连接测试也失败: {e2}")
            return False
    
    def test_gemini_api(self, api_key: str):
        """测试 Gemini API 连接"""
        print("测试 Gemini API 连接...")
        
        try:
            # 尝试导入 google.generativeai
            try:
                import google.generativeai as genai
            except ImportError:
                print("⚠️ google.generativeai 模块未安装")
                return False
            
            # 配置 API
            genai.configure(api_key=api_key)
            
            # 创建模型
            model = genai.GenerativeModel('gemini-2.5-flash')
            
            # 测试简单请求
            response = model.generate_content("Hello, this is a test.")
            
            if response and response.text:
                print("✓ Gemini API 连接成功")
                print(f"测试响应: {response.text[:100]}...")
                return True
            else:
                print("✗ Gemini API 响应为空")
                return False
                
        except Exception as e:
            print(f"✗ Gemini API 连接失败: {e}")
            if "User location is not supported" in str(e):
                print("提示：需要使用 VPN 或代理访问 Gemini API")
                print("注意：Google AI SDK 可能不完全支持 SOCKS5 代理")
                print("建议：使用 VPN 客户端或 HTTP 代理")
            return False


def convert_pdf_with_advanced_proxy(
    pdf_path: str,
    config_path: str = "config.json",
    output_dir: str = "./textbook_output",
    proxy_url: Optional[str] = None
) -> Dict[str, Any]:
    """
    使用高级代理支持转换 PDF
    """
    print(f"\n=== Marker PDF 转换器 (高级代理支持) ===")
    print(f"PDF 文件: {pdf_path}")
    print(f"配置文件: {config_path}")
    print(f"输出目录: {output_dir}")
    if proxy_url:
        print(f"代理: {proxy_url}")
    
    try:
        # 1. 设置代理
        proxy_manager = None
        if proxy_url:
            proxy_manager = ProxyManager(proxy_url)
            proxy_manager.setup_socks5_proxy()
            
            # 测试代理连接
            if not proxy_manager.test_connection():
                print("⚠️ 代理连接测试失败，但继续尝试转换...")
        
        # 2. 检查文件
        if not os.path.exists(pdf_path):
            return {"success": False, "error": f"PDF 文件不存在: {pdf_path}"}
        
        if not os.path.exists(config_path):
            return {"success": False, "error": f"配置文件不存在: {config_path}"}
        
        # 3. 加载配置
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print(f"✓ 加载配置: {config}")
        
        # 4. 测试 Gemini API（如果启用了 LLM）
        if config.get("use_llm", False) and proxy_manager:
            api_key = config.get("gemini_api_key")
            if api_key:
                if not proxy_manager.test_gemini_api(api_key):
                    print("⚠️ Gemini API 连接测试失败，但继续尝试转换...")
        
        # 5. 设置 API Key 环境变量
        if config.get("gemini_api_key"):
            os.environ["GEMINI_API_KEY"] = config["gemini_api_key"]
        
        # 6. 创建输出目录
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 7. 导入 marker 模块
        from marker.converters.pdf import PdfConverter
        from marker.models import create_model_dict
        from marker.output import text_from_rendered
        from marker.config.parser import ConfigParser
        
        # 8. 创建配置解析器和转换器
        start_time = time.time()
        
        config_parser = ConfigParser(config)
        converter = PdfConverter(
            artifact_dict=create_model_dict(),
            config=config_parser.generate_config_dict(),
            processor_list=config_parser.get_processors(),
            renderer=config_parser.get_renderer()
        )
        
        print("✓ 转换器创建成功，开始处理 PDF...")
        
        # 9. 执行转换
        rendered = converter(pdf_path)
        text, _, images = text_from_rendered(rendered)
        
        end_time = time.time()
        
        # 10. 保存结果
        pdf_name = Path(pdf_path).stem
        output_subdir = output_path / pdf_name
        output_subdir.mkdir(exist_ok=True)
        
        # 保存 Markdown
        markdown_path = output_subdir / f"{pdf_name}.md"
        with open(markdown_path, "w", encoding="utf-8") as f:
            f.write(text)
        
        # 保存元数据
        metadata = {
            "input_file": pdf_path,
            "output_dir": str(output_subdir),
            "config_used": config,
            "processing_time": end_time - start_time,
            "text_length": len(text),
            "num_images": len(images) if images else 0,
            "success": True,
            "proxy_used": proxy_url is not None,
            "proxy_url": proxy_url if proxy_url else None
        }
        
        metadata_path = output_subdir / "metadata.json"
        with open(metadata_path, "w", encoding="utf-8") as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        print(f"✓ 转换完成！")
        print(f"✓ 处理时间: {end_time - start_time:.2f}秒")
        print(f"✓ 文本长度: {len(text)} 字符")
        print(f"✓ 提取图片: {len(images) if images else 0} 张")
        print(f"✓ 输出保存到: {output_subdir}")
        print(f"✓ Markdown 文件: {markdown_path}")
        
        return metadata
        
    except Exception as e:
        print(f"✗ 转换失败: {str(e)}")
        traceback.print_exc()
        return {"success": False, "error": str(e)}


def main():
    """主函数"""
    print("=== Marker PDF 转换器 (高级代理支持版本) ===\n")
    
    # 配置参数
    pdf_file = "sample.pdf"
    config_file = "config.json"
    output_dir = "./textbook_output"
    
    # NordVPN SOCKS5 代理
    proxy_url = "socks5://2qfCDkqLEw8P3kf4ZAdm5zMu:<EMAIL>:1080"
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == "--no-proxy":
            proxy_url = None
            print("⚠️ 禁用代理模式")
        elif sys.argv[1] == "--test-proxy":
            # 仅测试代理连接
            proxy_manager = ProxyManager(proxy_url)
            proxy_manager.setup_socks5_proxy()
            proxy_manager.test_connection()
            
            # 测试 Gemini API
            if os.path.exists(config_file):
                with open(config_file, 'r') as f:
                    config = json.load(f)
                api_key = config.get("gemini_api_key")
                if api_key:
                    proxy_manager.test_gemini_api(api_key)
            return 0
        elif sys.argv[1].endswith('.pdf'):
            pdf_file = sys.argv[1]
    
    # 检查文件
    if not os.path.exists(pdf_file):
        print(f"错误：PDF 文件 '{pdf_file}' 不存在")
        print("用法:")
        print(f"  uv run python {sys.argv[0]}                    # 转换 sample.pdf")
        print(f"  uv run python {sys.argv[0]} your_file.pdf      # 转换指定文件")
        print(f"  uv run python {sys.argv[0]} --no-proxy         # 不使用代理")
        print(f"  uv run python {sys.argv[0]} --test-proxy       # 仅测试代理")
        return 1
    
    if not os.path.exists(config_file):
        print(f"错误：配置文件 '{config_file}' 不存在")
        return 1
    
    print(f"PDF 文件: {pdf_file}")
    print(f"配置文件: {config_file}")
    print(f"输出目录: {output_dir}")
    if proxy_url:
        print(f"代理: {proxy_url}")
    else:
        print("代理: 禁用")
    print()
    
    # 执行转换
    result = convert_pdf_with_advanced_proxy(
        pdf_path=pdf_file,
        config_path=config_file,
        output_dir=output_dir,
        proxy_url=proxy_url
    )
    
    if result.get("success", False):
        print("\n🎉 转换成功完成！")
        print(f"输出目录: {result.get('output_dir', output_dir)}")
        return 0
    else:
        print(f"\n❌ 转换失败: {result.get('error', '未知错误')}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
