import torch
from marker.converters.pdf import PdfConverter
from marker.models import create_model_dict
from marker.settings import Settings
import os
import time
import json
from pathlib import Path
from typing import Dict, List, Any
import traceback
import sys


# 不再硬編碼 API key，從環境變量或 .env 文件讀取


class MarkerConverterTester:
    """用於測試和比較不同 Marker Converter 的類"""
    
    def __init__(self, output_dir: str = "./converter_comparison"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.results = {}
        
    def test_pdf_converter(self, file_path: str, use_llm: bool = False) -> Dict[str, Any]:
        """測試 PDFConverter - 完整的 PDF 轉換"""
        print(f"\n=== 測試 PDFConverter {'(with Gemini 2.5 Flash)' if use_llm else '(without LLM)'} ===")
        
        try:
            from marker.converters.pdf import PdfConverter
            from marker.models import create_model_dict
            from marker.output import text_from_rendered
            from marker.config.parser import ConfigParser
            
            start_time = time.time()
            
            # 創建配置 - 使用 ConfigParser 來正確配置 LLM
            if use_llm:
                config_dict = {
                    "use_llm": True,
                    "gemini_api_key": os.environ.get('GEMINI_API_KEY'),
                    "gemini_model": "gemini-2.5-flash",  # 指定 Gemini 2.5 Flash
                    "format_lines": True,
                    "output_format": "markdown"
                }
                config_parser = ConfigParser(config_dict)
                
                # 創建轉換器（帶 LLM）
                converter = PdfConverter(
                    artifact_dict=create_model_dict(),
                    config=config_parser.generate_config_dict(),
                    processor_list=config_parser.get_processors(),
                    renderer=config_parser.get_renderer()
                )
            else:
                # 創建轉換器（不帶 LLM）
                converter = PdfConverter(
                    artifact_dict=create_model_dict()
                )
            
            # 執行轉換
            rendered = converter(file_path)
            
            # 提取結果
            text, _, images = text_from_rendered(rendered)
            
            end_time = time.time()
            
            # 保存結果
            output_subdir = self.output_dir / f"pdf_converter_{'gemini_2.5_flash' if use_llm else 'no_llm'}"
            output_subdir.mkdir(exist_ok=True)
            
            # 保存 Markdown
            with open(output_subdir / "output.md", "w", encoding="utf-8") as f:
                f.write(text)
            
            # 保存元數據
            metadata = {
                "converter": "PDFConverter",
                "use_llm": use_llm,
                "llm_model": "gemini-2.5-flash" if use_llm else None,
                "processing_time": end_time - start_time,
                "text_length": len(text),
                "num_images": len(images) if images else 0,
                "success": True
            }
            
            with open(output_subdir / "metadata.json", "w") as f:
                json.dump(metadata, f, indent=2)
            
            print(f"✓ 處理時間: {end_time - start_time:.2f}秒")
            print(f"✓ 文本長度: {len(text)} 字符")
            print(f"✓ 提取圖片: {len(images) if images else 0} 張")
            if use_llm:
                print(f"✓ 使用模型: Gemini 2.5 Flash")
            
            return metadata
            
        except Exception as e:
            print(f"✗ PDFConverter 失敗: {str(e)}")
            traceback.print_exc()
            return {"converter": "PDFConverter", "success": False, "error": str(e)}
    
    def test_table_converter(self, file_path: str, use_llm: bool = False) -> Dict[str, Any]:
        """測試 TableConverter - 專門提取表格"""
        print(f"\n=== 測試 TableConverter {'(with LLM)' if use_llm else '(without LLM)'} ===")
        
        try:
            from marker.converters.table import TableConverter
            from marker.models import create_model_dict
            
            start_time = time.time()
            
            # 創建表格轉換器
            converter = TableConverter(
                artifact_dict=create_model_dict(),
            )
            
            # 執行轉換
            rendered = converter(file_path)
            
            end_time = time.time()
            
            # 保存結果
            output_subdir = self.output_dir / f"table_converter_{'llm' if use_llm else 'no_llm'}"
            output_subdir.mkdir(exist_ok=True)
            
            # 根據輸出格式保存
            if hasattr(rendered, 'markdown'):
                with open(output_subdir / "tables.md", "w", encoding="utf-8") as f:
                    f.write(rendered.markdown)
                text_length = len(rendered.markdown)
            else:
                # JSON 格式輸出
                with open(output_subdir / "tables.json", "w") as f:
                    json.dump(rendered.model_dump() if hasattr(rendered, 'model_dump') else str(rendered), f, indent=2)
                text_length = len(str(rendered))
            
            metadata = {
                "converter": "TableConverter",
                "use_llm": use_llm,
                "processing_time": end_time - start_time,
                "output_length": text_length,
                "success": True
            }
            
            with open(output_subdir / "metadata.json", "w") as f:
                json.dump(metadata, f, indent=2)
            
            print(f"✓ 處理時間: {end_time - start_time:.2f}秒")
            print(f"✓ 輸出長度: {text_length} 字符")
            
            return metadata
            
        except Exception as e:
            print(f"✗ TableConverter 失敗: {str(e)}")
            traceback.print_exc()
            return {"converter": "TableConverter", "success": False, "error": str(e)}
    
    def test_ocr_converter(self, file_path: str, keep_chars: bool = False) -> Dict[str, Any]:
        """測試 OCRConverter - 純 OCR 功能"""
        print(f"\n=== 測試 OCRConverter {'(保留字符級信息)' if keep_chars else '(標準模式)'} ===")
        
        try:
            from marker.converters.ocr import OCRConverter
            from marker.models import create_model_dict
            from marker.config.parser import ConfigParser
            
            start_time = time.time()
            
            # 創建配置字典
            config_dict = {
                "keep_chars": keep_chars,
                "output_format": "json"
            }
            
            # 使用 ConfigParser 創建適當的配置
            config_parser = ConfigParser(config_dict)
            
            # 創建 OCR 轉換器
            converter = OCRConverter(
                config=config_parser.generate_config_dict(),
                artifact_dict=create_model_dict(),
                processor_list=config_parser.get_processors(),
                renderer=config_parser.get_renderer()
            )
            
            # 執行轉換
            rendered = converter(file_path)
            
            end_time = time.time()
            
            # 保存結果
            output_subdir = self.output_dir / f"ocr_converter_{'chars' if keep_chars else 'standard'}"
            output_subdir.mkdir(exist_ok=True)
            
            # 保存 OCR 結果，添加 None 檢查和更好的錯誤處理
            if rendered is None:
                ocr_text = "OCR 轉換返回 None"
            elif hasattr(rendered, 'model_dump'):
                # 如果是 Pydantic 模型，使用 model_dump
                ocr_data = rendered.model_dump()
                ocr_text = json.dumps(ocr_data, indent=2, ensure_ascii=False)
            elif hasattr(rendered, 'children') and rendered.children is not None:
                # 嘗試從 children 提取文本
                try:
                    ocr_text = json.dumps(rendered.children, indent=2, ensure_ascii=False)
                except:
                    ocr_text = str(rendered.children)
            else:
                ocr_text = str(rendered) if rendered is not None else "OCR 結果為 None"
                
            with open(output_subdir / "ocr_output.txt", "w", encoding="utf-8") as f:
                f.write(ocr_text)
            
            metadata = {
                "converter": "OCRConverter",
                "keep_chars": keep_chars,
                "processing_time": end_time - start_time,
                "text_length": len(ocr_text),
                "success": True
            }
            
            with open(output_subdir / "metadata.json", "w") as f:
                json.dump(metadata, f, indent=2)
            
            print(f"✓ 處理時間: {end_time - start_time:.2f}秒")
            print(f"✓ OCR 文本長度: {len(ocr_text)} 字符")
            
            return metadata
            
        except Exception as e:
            print(f"✗ OCRConverter 失敗: {str(e)}")
            # 根據錯誤類型提供更詳細的信息
            if "'NoneType' object is not iterable" in str(e):
                print("這個錯誤通常是由於 marker-pdf 內部處理時遇到 None 值造成的")
                print("可能的原因：PDF 結構複雜或 OCR 處理過程中出現問題")
                print("建議：嘗試使用 PDFConverter 替代，它有更好的錯誤處理")
            traceback.print_exc()
            return {"converter": "OCRConverter", "success": False, "error": str(e)}
    
    def test_with_command_line(self, file_path: str, converter_type: str, extra_args: str = "") -> Dict[str, Any]:
        """使用命令行測試指定的 Converter"""
        print(f"\n=== 使用命令行測試 {converter_type} ===")
        
        start_time = time.time()
        
        output_subdir = self.output_dir / f"cli_{converter_type.lower()}"
        output_subdir.mkdir(exist_ok=True)
        
        # marker_single 只接受文件路徑參數，輸出到當前目錄
        # 我們需要移動到輸出目錄執行命令
        original_dir = os.getcwd()
        os.chdir(output_subdir)
        
        try:
            # 構建命令，使用相對路徑
            relative_path = os.path.relpath(file_path, output_subdir)
            cmd = f"marker_single '{relative_path}' {extra_args}"
            
            print(f"執行命令: {cmd}")
            print(f"在目錄: {output_subdir}")
            result = os.system(cmd)
            
            end_time = time.time()
            
            metadata = {
                "converter": f"CLI_{converter_type}",
                "command": cmd,
                "working_directory": str(output_subdir),
                "processing_time": end_time - start_time,
                "return_code": result,
                "success": result == 0
            }
            
            print(f"✓ 處理時間: {end_time - start_time:.2f}秒")
            print(f"✓ 返回碼: {result}")
            
        except Exception as e:
            print(f"✗ 命令執行失敗: {str(e)}")
            metadata = {
                "converter": f"CLI_{converter_type}",
                "command": cmd,
                "processing_time": time.time() - start_time,
                "return_code": -1,
                "success": False,
                "error": str(e)
            }
        finally:
            # 恢復原始目錄
            os.chdir(original_dir)
            
            # 保存元數據
            with open(output_subdir / "metadata.json", "w") as f:
                json.dump(metadata, f, indent=2)
        
        return metadata
    
    def run_comprehensive_test(self, file_path: str, use_gemini: bool = False):
        """對單個文件運行所有 Converter 的測試"""
        print(f"\n{'='*60}")
        print(f"開始測試文件: {file_path}")
        print(f"{'='*60}")
        
        file_name = Path(file_path).name
        self.results[file_name] = {}
        
        # 1. 測試 PDFConverter（不使用 LLM）
        self.results[file_name]["pdf_no_llm"] = self.test_pdf_converter(file_path, use_llm=False)
        
        # 2. 測試 PDFConverter（使用 LLM，如果配置了）
        if use_gemini:
            self.results[file_name]["pdf_with_llm"] = self.test_pdf_converter(file_path, use_llm=True)
        
        # 3. 測試 TableConverter
        self.results[file_name]["table"] = self.test_table_converter(file_path)
        
        # 4. 測試 OCRConverter (暫時跳過，因為有 None 錯誤)
        # self.results[file_name]["ocr"] = self.test_ocr_converter(file_path)
        print("\n=== 跳過 OCRConverter 測試 (已知錯誤) ===")
        self.results[file_name]["ocr"] = {"converter": "OCRConverter", "success": False, "error": "跳過測試 - 已知 NoneType 錯誤"}
        
        # 5. 使用命令行測試 TableConverter
        self.results[file_name]["cli_table"] = self.test_with_command_line(
            file_path, 
            "TableConverter",
            "--converter_cls marker.converters.table.TableConverter --output_format json"
        )
        
        # 6. 使用命令行測試 OCR Converter
        self.results[file_name]["cli_ocr"] = self.test_with_command_line(
            file_path,
            "OCRConverter",
            "--converter_cls marker.converters.ocr.OCRConverter --output_format json"
        )
        
        # 7. 如果配置了 Gemini，使用命令行測試帶 LLM 的轉換
        if use_gemini:
            # 使用正確的參數格式啟用 LLM
            self.results[file_name]["cli_llm"] = self.test_with_command_line(
                file_path,
                "PDFConverter_with_LLM",
                f"--use_llm --gemini_api_key {os.environ.get('GEMINI_API_KEY')}"
            )
    
    def generate_comparison_report(self):
        """生成比較報告"""
        report_path = self.output_dir / "comparison_report.md"
        
        with open(report_path, "w", encoding="utf-8") as f:
            f.write("# Marker Converters 比較報告\n\n")
            
            for file_name, file_results in self.results.items():
                f.write(f"## 文件: {file_name}\n\n")
                
                # 創建比較表格
                f.write("| Converter | 成功 | 處理時間(秒) | 輸出大小 | 特點 |\n")
                f.write("|-----------|------|--------------|----------|------|\n")
                
                for test_name, result in file_results.items():
                    success = "✓" if result.get("success", False) else "✗"
                    time_taken = f"{result.get('processing_time', 0):.2f}"
                    
                    # 獲取輸出大小
                    size = result.get('text_length', result.get('output_length', 'N/A'))
                    
                    # 特點描述
                    features = []
                    if 'llm' in test_name:
                        features.append("使用 LLM")
                    if 'table' in test_name:
                        features.append("專注表格")
                    if 'ocr' in test_name:
                        features.append("純 OCR")
                    if 'cli' in test_name:
                        features.append("命令行")
                    if test_name == 'pdf_no_llm':
                        features = ["標準轉換"]
                    
                    feature_str = ", ".join(features) if features else "標準轉換"
                    
                    f.write(f"| {test_name} | {success} | {time_taken} | {size} | {feature_str} |\n")
                
                f.write("\n")
            
            # 添加總結
            f.write("## 總結\n\n")
            f.write("### 各 Converter 的適用場景：\n\n")
            f.write("- **PDFConverter**: 適用於需要完整轉換 PDF 的場景，保留所有內容和格式\n")
            f.write("- **TableConverter**: 專門用於提取和轉換表格數據，適合處理大量表格的文檔\n")
            f.write("- **OCRConverter**: 用於純文本提取，特別是掃描文檔或圖片 PDF\n")
            f.write("- **使用 LLM**: 可以顯著提高複雜文檔的轉換質量，但會增加處理時間\n")
        
        print(f"\n報告已生成: {report_path}")


def load_env_file():
    """Load environment variables from .env file"""
    env_path = Path(".env")
    if env_path.exists():
        with open(env_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    # Only set if not already in environment
                    if key not in os.environ:
                        os.environ[key] = value


def test_single_file(file_path: str, use_gemini: bool = False, output_dir: str = "./converter_comparison"):
    """測試單個文件的函數，供外部調用"""
    if not os.path.exists(file_path):
        print(f"錯誤：文件 '{file_path}' 不存在")
        return None
    
    tester = MarkerConverterTester(output_dir)
    tester.run_comprehensive_test(file_path, use_gemini=use_gemini)
    tester.generate_comparison_report()
    
    return tester.results


# 主測試函數
def main():
    """主測試流程 - 適用於本地環境"""
    print("=== Marker Converters 比較測試 ===\n")
    
    # 加載環境變量
    load_env_file()
    
    # 檢查命令行參數
    if len(sys.argv) > 1:
        pdf_files = sys.argv[1:]
    else:
        # 如果沒有提供參數，使用默認的 sample.pdf
        if os.path.exists("sample.pdf"):
            pdf_files = ["sample.pdf"]
        else:
            print("用法: python ocrtest.py <pdf_file1> <pdf_file2> ...")
            print("或將 PDF 文件命名為 sample.pdf 放在當前目錄")
            return
    
    # 檢查是否使用 Gemini
    use_gemini = False
    gemini_key = os.getenv("GEMINI_API_KEY")
    if gemini_key and gemini_key != "your_gemini_api_key_here":
        use_gemini = True
        print("✓ 檢測到 GEMINI_API_KEY，將啟用 LLM 功能")
    else:
        print("⚠️  未檢測到有效的 GEMINI_API_KEY，將不使用 LLM 功能")
    
    # 創建測試器
    tester = MarkerConverterTester()
    
    # 對每個文件進行測試
    for pdf_file in pdf_files:
        if os.path.exists(pdf_file):
            tester.run_comprehensive_test(pdf_file, use_gemini=use_gemini)
        else:
            print(f"警告：文件 '{pdf_file}' 不存在，跳過")
    
    # 生成比較報告
    if tester.results:
        tester.generate_comparison_report()
        print(f"\n所有測試完成！結果保存在: {tester.output_dir}")
    else:
        print("\n沒有成功測試任何文件")


if __name__ == "__main__":
    # 確保先導入 torch 避免循環導入問題
    import torch
    import torchvision
    print(f"PyTorch version: {torch.__version__}")
    
    # 運行主測試
    main()